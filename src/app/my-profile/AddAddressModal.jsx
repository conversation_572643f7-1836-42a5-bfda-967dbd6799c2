import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import toast from "react-hot-toast";
import countryData from "@/lib/data/country.json";
import { postAddress } from "@/lib/features/addresses/addressSlice";

export default function AddAddressModal({ isOpen, onClose }) {
  const dispatch = useDispatch();

  const [formData, setFormData] = useState({
    name: "",
    type: "Home",
    phone: "",
    email: "",
    city: "",
    address: "",
    state: "",
    country: "",
    zip_code: "",
    is_default: false,
  });

  useEffect(() => {
    if (!isOpen) {
      setFormData({
        name: "",
        type: "Home",
        phone: "",
        email: "",
        city: "",
        address: "",
        state: "",
        country: "",
        zip_code: "",
        is_default: false,
      });
    }
  }, [isOpen]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const payload = { ...formData };
      await dispatch(postAddress(payload)).unwrap();
      toast.success("Address added successfully!");
      onClose();
    } catch (error) {
      toast.error("Failed to add address!");
    }
  };

  return (
    isOpen && (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
        <div className="bg-white w-full max-w-lg md:max-w-2xl rounded-lg shadow-lg p-6 overflow-y-auto max-h-screen">
          <h2 className="text-xl font-semibold mb-4">Add New Address</h2>
          <form
            onSubmit={handleSubmit}
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
          >
            {/* Full Name */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Full Name
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Enter your full name"
                className="w-full border rounded-lg px-3 py-2 text-sm"
              />
            </div>

            {/* Address Type */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Address Type
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full border rounded-lg px-3 py-2 text-sm"
              >
                <option value="Home">Home</option>
                <option value="Work">Work</option>
                <option value="Other">Other</option>
              </select>
            </div>

            {/* Mobile Number */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Mobile Number
              </label>
              <input
                type="text"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="Enter mobile number"
                className="w-full border rounded-lg px-3 py-2 text-sm"
              />
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium mb-1">Email</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter email"
                className="w-full border rounded-lg px-3 py-2 text-sm"
              />
            </div>

            {/* Address Line 1 */}
            <div>
              <label className="block text-sm font-medium mb-1">Address</label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleChange}
                placeholder="House/Flat number, Street"
                className="w-full border rounded-lg px-3 py-2 text-sm"
              />
            </div>

            {/* City */}
            <div>
              <label className="block text-sm font-medium mb-1">City</label>
              <input
                type="text"
                name="city"
                value={formData.city}
                onChange={handleChange}
                placeholder="Enter city"
                className="w-full border rounded-lg px-3 py-2 text-sm"
              />
            </div>

            {/* State */}
            <div>
              <label className="block text-sm font-medium mb-1">State</label>
              <input
                type="text"
                name="state"
                value={formData.state}
                onChange={handleChange}
                placeholder="Enter state"
                className="w-full border rounded-lg px-3 py-2 text-sm"
              />
            </div>

            {/* Country */}
            <div>
              <label className="block text-sm font-medium mb-1">Country</label>
              <select
                name="country"
                value={formData.country}
                onChange={handleChange}
                className="w-full border rounded-lg px-3 py-2 text-sm"
              >
                <option value="">Select country</option>
                {countryData.map((country) => (
                  <option
                    key={country.country_name}
                    value={country.country_name.toString()}
                  >
                    {country.country_name}
                  </option>
                ))}
              </select>
            </div>

            {/* ZIP Code */}
            <div>
              <label className="block text-sm font-medium mb-1">ZIP Code</label>
              <input
                type="text"
                name="zip_code"
                value={formData.zip_code}
                onChange={handleChange}
                placeholder="Enter ZIP code"
                className="w-full border rounded-lg px-3 py-2 text-sm"
              />
            </div>

            {/* Set as Default Address */}
            <div className="flex items-center">
              <input
                type="checkbox"
                name="is_default"
                checked={formData.is_default}
                onChange={handleChange}
                id="defaultAddress"
                className="h-4 w-4 text-blue-600 border-gray-300 rounded"
              />
              <label
                htmlFor="defaultAddress"
                className="ml-2 text-sm text-gray-700"
              >
                Set as default address
              </label>
            </div>

            {/* Buttons */}
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-lg"
              >
                Save Address
              </button>
            </div>
          </form>
        </div>
      </div>
    )
  );
}
